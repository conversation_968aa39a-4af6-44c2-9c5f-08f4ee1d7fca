import 'dart:convert'; // Required for JSON parsing
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/core/widgets/common/hero_widget.dart';
import 'package:kairos/core/enums/header_enums.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/features/finances/presentation/bloc/finances_cubit.dart';
import 'package:kairos/features/finances/presentation/bloc/finances_state.dart';
import 'package:kairos/features/profile/domain/entities/carte_virtuelle_entity.dart'; // Import CarteVirtuelleEntity
import 'package:kairos/features/profile/presentation/bloc/profile_cubit.dart'; // Import ProfileCubit
import 'package:kairos/features/profile/presentation/bloc/profile_state.dart';
import 'package:kairos/core/widgets/common/snackbar_widget.dart';
import 'package:kairos/core/widgets/indicators/custom_spinner.dart';

import 'package:kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:kairos/core/utils/navigation_utils.dart';
import 'package:kairos/core/di/injection_container.dart';

import 'package:kairos/features/finances/presentation/pages/finances/finances_widgets/finance_item.widget.dart';
import 'package:mobile_scanner/mobile_scanner.dart'; // Import for MobileScanner

class ControleFinancierPage extends StatefulWidget {
  final EtablissementUtilisateur school;
  final String scannedJsonValue;

  const ControleFinancierPage({
    super.key,
    required this.school,
    required this.scannedJsonValue,
  });

  @override
  State<ControleFinancierPage> createState() => _ControleFinancierPageState();
}

class _ControleFinancierPageState extends State<ControleFinancierPage> {
  bool _isUnpaidFeesExpanded = false;
  late CarteVirtuelleEntity _initialCarteVirtuelle; // State variable for initial scanned data
  CarteVirtuelleEntity? _currentCarteVirtuelle; // State variable for currently displayed data

  @override
  void initState() {
    super.initState();
    _parseAndInitializeCarteVirtuelle(); // Parse scanned JSON and initialize
    _initializeFinancialCheck();
  }

  void _parseAndInitializeCarteVirtuelle() {
    try {
      final Map<String, dynamic> jsonMap = json.decode(widget.scannedJsonValue);
      _initialCarteVirtuelle = CarteVirtuelleEntity.fromJson(jsonMap);
      _currentCarteVirtuelle = _initialCarteVirtuelle; // Initialize with scanned data
    } catch (e) {
      // Handle parsing error, e.g., show a snackbar or navigate back
      debugPrint('Error parsing scanned JSON: $e');
      // Optionally, show an error message to the user
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          CustomSnackbar(message: 'Erreur lors de la lecture du code QR. Veuillez réessayer.', isError: true).getSnackBar(),
        );
        // Navigator.pop(context); // Removed: Do not pop the page on parsing failure
      });
    }
  }

  Future<void> _initializeFinancialCheck() async {
    // Load finances data with financial status check
    final financesCubit = context.read<FinancesCubit>();
    final profileCubit = context.read<ProfileCubit>(); // Inject ProfileCubit
    final authLocalDataSource = sl<AuthLocalDataSource>();

    // Get student code from initial scanned value
    final codeEtudiant = _initialCarteVirtuelle.codeUtilisateur;

    if (codeEtudiant.isNotEmpty) {
      // Get phone number from SharedPreferences
      final phoneNumber = await authLocalDataSource.getPhoneNumber();

      if (phoneNumber != null) {
        // Load finances data with financial status
        financesCubit.loadFinancesDataWithStatus(
          codeEtab: widget.school.codeEtab,
          telephone: phoneNumber,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: widget.school.codeUtilisateur,
        );

        // Invoke getCarteVirtuelle from ProfileCubit
        profileCubit.getCarteVirtuelle(
          codeEtab: widget.school.codeEtab,
          telephone: phoneNumber,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: widget.school.codeUtilisateur,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      extendBodyBehindAppBar: false,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
      ),
      body: CustomScrollView(
        slivers: [
          // Hero Widget Section
          SliverToBoxAdapter(
            child: HeroWidget(
              pageSection: HeaderEnum.controleFinancier,
              etablissementUser: widget.school,
              carteVirtuelle: _currentCarteVirtuelle, // Pass current CarteVirtuelleEntity
            ),
          ),
          // Main Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 7.0),
              child: Column(
                children: [
                  const SizedBox(height: 15),
                  // Financial Status Section
                  BlocListener<ProfileCubit, ProfileState>(
                    listener: (context, profileState) {
                      if (profileState is CarteVirtuelleLoaded) {
                        setState(() {
                          _currentCarteVirtuelle = profileState.carteVirtuelle;
                        });
                      } else if (profileState is CarteVirtuelleError) {
                        // Optionally show an error message if virtual card loading fails
                        ScaffoldMessenger.of(context).showSnackBar(
                          CustomSnackbar(message: 'Erreur lors du chargement de la carte virtuelle: ${profileState.message}', isError: true).getSnackBar(),
                        );
                      }
                    },
                    child: BlocBuilder<FinancesCubit, FinancesState>(
                      builder: (context, financesState) {
                        debugPrint('financesState ------>: $financesState');
                        if (financesState is FinancesLoading) {
                          return const Center(
                            child: CustomSpinner(),
                          );
                        } else if (financesState is FinancesError) {
                          return Center(
                            child: Column(
                              children: [
                                const Icon(
                                  Icons.error_outline,
                                  color: Colors.red,
                                  size: 48,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Erreur lors de la vérification du statut financier',
                                  style: const TextStyle(
                                    color: Colors.red,
                                    fontSize: 16,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  financesState.message,
                                  style: const TextStyle(
                                    color: Colors.grey,
                                    fontSize: 14,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          );
                        } else if (financesState is FinancesLoaded &&
                                   financesState.financialStatus != null) {
                                    
                        debugPrint('financesState.financialStatus ------>: ${(financesState as FinancesLoaded?)?.financialStatus})}');
                        debugPrint('financesState.financialStatus.enRegle ------>: ${(financesState as FinancesLoaded?)?.financialStatus?.enRegle}');
                          return _buildStatusSection(financesState.financialStatus!.enRegle);
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                  ),

                  const SizedBox(height: 15),

                  // Unpaid Fees Section (only show if not "en règle")
                  BlocBuilder<FinancesCubit, FinancesState>(
                    builder: (context, financesState) {
                      if (financesState is FinancesLoaded &&
                          financesState.financialStatus != null &&
                          !financesState.financialStatus!.enRegle) {
                            debugPrint('financesState.unpaidFeesResponse ------>: ${(financesState as FinancesLoaded?)?.unpaidFeesResponse})}');
                        return _buildUnpaidFeesSection();
                      }
                      return const SizedBox.shrink();
                    },
                  ),

                  const SizedBox(height: 20),

                  // New Control Button
                  _buildNewControlButton(),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusSection(bool enRegle) {
    if (enRegle) {
      return _buildSuccessStatus();
    } else {
      return _buildAlertStatus();
    }
  }

  Widget _buildSuccessStatus() {
    // Success UI based on Figma design (node 453-1471)
    return SizedBox(
      width: double.infinity,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Success icon
          Container(
            width: 63,
            height: 63,
            decoration: const BoxDecoration(
              color: Color(0xFF3EB290),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.check,
              color: Colors.white,
              size: 32,
            ),
          ),
          const SizedBox(width: 15),
          // Status text
          const Text(
            'EN REGLE',
            style: TextStyle(
              fontFamily: 'Roboto',
              fontWeight: FontWeight.w500,
              fontSize: 36,
              color: Color(0xFF3EB290),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlertStatus() {
    // Alert UI based on Figma design (node 507-1103)
    return SizedBox(
      width: double.infinity,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Alert icon
          Container(
            width: 63,
            height: 63,
            decoration: const BoxDecoration(
              color: Color(0xFFFF3B30),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.close,
              color: Colors.white,
              size: 32,
            ),
          ),
          const SizedBox(width: 15),
          // Status text
          const Text(
            'PAS EN REGLE',
            style: TextStyle(
              fontFamily: 'Roboto',
              fontWeight: FontWeight.w500,
              fontSize: 36,
              color: Color(0xFFFF3B30),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnpaidFeesSection() {
    return BlocBuilder<FinancesCubit, FinancesState>(
      builder: (context, financesState) {
        if (financesState is FinancesLoading) {
          return const Center(child: CustomSpinner());
        } else if (financesState is FinancesError) {
          return Center(
            child: Text(
              'Erreur lors du chargement des frais impayés',
              style: TextStyle(color: Colors.red),
            ),
          );
        } else if (financesState is FinancesLoaded) {
          final unpaidFees = financesState.unpaidFeesResponse?.fraisImPayes ?? [];
          
          if (unpaidFees.isEmpty) {
            return const SizedBox.shrink();
          }

          // Calculate total unpaid amount
          final totalUnpaid = unpaidFees.fold<double>(
            0.0,
            (sum, fee) => sum + (fee.montantFrais?.toDouble() ?? 0.0),
          );

          return Column(
            children: [
              // Unpaid fees accordion header
              GestureDetector(
                onTap: () {
                  setState(() {
                    _isUnpaidFeesExpanded = !_isUnpaidFeesExpanded;
                  });
                },
                child: Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.black, width: 1),
                    ),
                  ),
                  child: SizedBox(
                    height: 30,
                    child: Stack(
                      children: [
                        // Orange indicator line
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: Container(
                            height: 7,
                            color: const Color(0xFFE5920C),
                          ),
                        ),
                        // Content
                        Positioned(
                          left: 33,
                          top: 0,
                          right: 33,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'FRAIS IMPAYÉS ${totalUnpaid.toInt()} XOF',
                                style: const TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w400,
                                  fontSize: 16,
                                  color: Colors.black,
                                ),
                              ),
                              Icon(
                                _isUnpaidFeesExpanded
                                    ? Icons.expand_less
                                    : Icons.expand_more,
                                size: 18,
                                color: Colors.black,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Expandable unpaid fees list
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                height: _isUnpaidFeesExpanded ? null : 0,
                child: _isUnpaidFeesExpanded
                    ? Column(
                        children: unpaidFees.map((fee) {
                          return FinanceItem(
                            intituleFrais: fee.intituleFrais,
                            montantFrais: (fee.montantFrais ?? 0).toInt(),
                            status: 'Aucune quittance générée',
                            isPaid: false,
                            isObligatory: fee.indicateur ?? false,
                            montantEncaisseAjour: fee.montantEncaisseAjour?.toInt(),
                            dateEchance: fee.dateEchance,
                            dateQuittance: fee.dateQuittance,
                            numeroQuittance: fee.numeroQuittance,
                          );
                        }).toList(),
                      )
                    : const SizedBox.shrink(),
              ),
            ],
          );
        }
        
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildNewControlButton() {
    return Container(
      width: 335,
      height: 50,
      decoration: BoxDecoration(
        color: const Color(0xFF06B6E4),
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF16196E).withValues(alpha: 0.08),
            offset: const Offset(0, 6),
            blurRadius: 16,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(10),
          onTap: () async{
            // Handle new control action
            await _handleNewControl();
          },
          child: const Center(
            child: Text(
              'NOUVEAU CONTRÔLE',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w600,
                fontSize: 16,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleNewControl() async {
    try {
      // Launch QR code scanner
      await NavigationUtils.push(context,
        MobileScanner(
            controller: MobileScannerController(
              detectionSpeed: DetectionSpeed.normal,
              formats: const [BarcodeFormat.qrCode],
            ),
            // Add overlayBuilder to display a button on top of the scanner
            overlayBuilder: (context, rect) {
              return Align(
              alignment: Alignment(0, .5), // Align to the bottom
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 20.0), // Add padding from the bottom
                  child: Container(
                    width: 335, // Fixed width as per Figma
                    height: 50, // Fixed height as per Figma
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5), // Button color from Figma
                      borderRadius: BorderRadius.circular(10), // Rounded corners
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF16196E).withValues(alpha: 0.08), // Shadow color and opacity
                          offset: const Offset(0, 6), // Shadow offset
                          blurRadius: 16, // Shadow blur
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent, // Make Material transparent to show Container's color
                      child: InkWell(
                        borderRadius: BorderRadius.circular(10), // Match Container's border radius
                        onTap: () {
                          // Placeholder for button functionality
                          ScaffoldMessenger.of(context).showSnackBar(
                            CustomSnackbar(message: 'Fonctionnalité en cours de développement', isError: false).getSnackBar(),
                          );
                        },
                        child: const Center(
                          child: Row(
                            mainAxisSize: MainAxisSize.min, // Use minimum space for the row
                            children: [
                              Icon(
                                Icons.edit, // Edit icon
                                color: Colors.white, // Icon color
                                size: 20, // Icon size
                              ),
                              SizedBox(width: 10), // Spacing between icon and text
                              Text(
                                'Saisir le matricule de l’étudiant', // Button text
                                style: TextStyle(
                                  fontWeight: FontWeight.w600, // Font weight
                                  fontSize: 16, // Font size
                                  color: Colors.white, // Text color
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
            onDetect: (BarcodeCapture capture) async {
              // Handle barcode detection
              final String? scannedValue = capture.barcodes.first.rawValue;
              debugPrint('ControlefinancierPage --> MobileScanner : Scanned value: $scannedValue');
              if (scannedValue != null && scannedValue.isNotEmpty) {
                NavigationUtils.pop(context); // Close scanner

                // Update the page's state with the new scanned value
                setState(() {
                  _initialCarteVirtuelle = CarteVirtuelleEntity.fromJson(json.decode(scannedValue));
                  _currentCarteVirtuelle = _initialCarteVirtuelle;
                });

                // Re-initialize financial check with the new data
               await _initializeFinancialCheck();
                debugPrint('ControlefinancierPage --> MobileScanner : Financial check initialized with new data');
                ScaffoldMessenger.of(context).showSnackBar(
                  CustomSnackbar(message: 'Nouveau contrôle effectué avec succès!', isError: false).getSnackBar(),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  CustomSnackbar(message: 'Aucune valeur scannée détectée.', isError: true).getSnackBar(),
                );
              }
            },
          ),
        );
    } catch (e) {
      // Handle scanner errors
      debugPrint('QR Scanner error: $e');

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'ouverture du scanner QR: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
