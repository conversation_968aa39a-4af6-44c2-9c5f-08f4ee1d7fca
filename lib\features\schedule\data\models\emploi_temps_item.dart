import '../../domain/entities/emploi_temps_entity.dart';

/// Data model for schedule/timetable items from API
class EmploiTempsItem {
  final String date;
  final String couleur;
  final String heure;
  final String salle;
  final String cours;
  final String professeur;
  final String classe;
  final String semestre;
  final String type;
  final int? emploiDuTempsId;
  final int? responsableId;

  const EmploiTempsItem({
    required this.date,
    required this.couleur,
    required this.heure,
    required this.salle,
    required this.cours,
    required this.professeur,
    required this.classe,
    required this.semestre,
    required this.type,
    this.emploiDuTempsId,
    this.responsableId,
  });

  /// Create EmploiTempsItem from JSON
  factory EmploiTempsItem.fromJson(Map<String, dynamic> json) {
    return EmploiTempsItem(
      date: json['date'] ?? '',
      couleur: json['couleur'] ?? '',
      heure: json['heure'] ?? '',
      salle: json['salle'] ?? '',
      cours: json['cours'] ?? '',
      professeur: json['professeur'] ?? '',
      classe: json['classe'] ?? '',
      semestre: json['semestre'] ?? '',
      type: json['type'] ?? '',
      emploiDuTempsId: json['emploiDuTempsId'],
      responsableId: json['responsableId'],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'couleur': couleur,
      'heure': heure,
      'salle': salle,
      'cours': cours,
      'professeur': professeur,
      'classe': classe,
      'semestre': semestre,
      'type': type,
      'emploiDuTempsId': emploiDuTempsId,
      'responsableId': responsableId,
    };
  }

  /// Convert to domain entity
  EmploiTempsEntity toEntity() {
    return EmploiTempsEntity(
      date: date,
      couleur: couleur,
      heure: heure,
      salle: salle,
      cours: cours,
      professeur: professeur,
      classe: classe,
      semestre: semestre,
      type: type,
      emploiDuTempsId: emploiDuTempsId,
      responsableId: responsableId,
    );
  }
}