import 'package:kairos/features/educational_resources/domain/entities/resource_pedagogique_entity.dart';
import 'document_model.dart';

/// Data model for an educational resource item
class ResourcePedagogiqueModel {
  final int idRessource;
  final String description;
  final String professeur;
  final String classe;
  final String dateAjout;
  final List<DocumentModel> documents;

  ResourcePedagogiqueModel({
    required this.idRessource,
    required this.description,
    required this.professeur,
    required this.classe,
    required this.dateAjout,
    required this.documents,
  });

  /// Factory constructor to create ResourcePedagogiqueModel from JSON
  factory ResourcePedagogiqueModel.fromJson(Map<String, dynamic> json) {
    return ResourcePedagogiqueModel(
      idRessource: json['idRessource'] ?? 0,
      description: json['description'] ?? '',
      professeur: json['professeur'] ?? '',
      classe: json['classe'] ?? '',
      dateAjout: json['dateAjout'] ?? '',
      documents: (json['documents'] as List<dynamic>?)
          ?.map((doc) => DocumentModel.fromJson(doc as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  /// Method to convert ResourcePedagogiqueModel to JSON (optional, but good practice)
  Map<String, dynamic> toJson() {
    return {
      'idRessource': idRessource,
      'description': description,
      'professeur': professeur,
      'classe': classe,
      'dateAjout': dateAjout,
      'documents': documents.map((doc) => doc.toJson()).toList(),
    };
  }

  /// Method to convert ResourcePedagogiqueModel to ResourcePedagogiqueEntity
  ResourcePedagogiqueEntity toEntity() {
    return ResourcePedagogiqueEntity(
      idRessource: idRessource,
      description: description,
      professeur: professeur,
      classe: classe,
      dateAjout: dateAjout,
      documents: documents.map((doc) => doc.toEntity()).toList(),
    );
  }
}
