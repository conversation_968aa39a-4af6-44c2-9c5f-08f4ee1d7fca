import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image/image.dart' hide Image; // Hide the Image class to avoid conflict with <PERSON><PERSON><PERSON>'s Image
import 'package:image/image.dart' as img_lib; // Use a prefix for image library functions

/// Adds a background color to a transparent PNG image from a base64 string.
///
/// This function decodes the base64 string, creates a new image with the
/// specified background color, draws the original image onto it, and then
/// encodes the result back into a base64 string.
///
/// [base64String] The base64 encoded string of the transparent PNG image.
/// [backgroundColor] The color to use as the background.
/// [width] The desired width of the output image. If null, uses original width.
/// [height] The desired height of the output image. If null, uses original height.
///
/// Returns a Future that completes with the new base64 encoded string
/// of the image with the background, or null if an error occurs.
 Uint8List? addBackgroundColorToBase64Png(String base64String, Color backgroundColor, {int? width, int? height}) {
  try {
    // Decode the base64 string to bytes
    final Uint8List bytes = base64Decode(base64String);

    // Decode the image using the image package
    img_lib.Image? originalImage = img_lib.decodePng(bytes);

    if (originalImage == null) {
      return null; // Could not decode image
    }

    // Determine the dimensions for the new image
    final int finalWidth = width ?? originalImage.width;
    final int finalHeight = height ?? originalImage.height;

    // Create a new image with the desired background color
    // Ensure the new image has the same dimensions as the original or specified
    img_lib.Image newImage = img_lib.Image(width: finalWidth, height: finalHeight);

    // Fill the new image with the background color
    img_lib.fill(newImage, color: backgroundColor);

    // Draw the original image onto the new image.
    // This will overlay the transparent PNG on top of the background.
    final dst = img_lib.compositeImage(newImage, originalImage, blend: BlendMode.direct);

    // Encode the new image back to PNG bytes
    final Uint8List newBytes = Uint8List.fromList(img_lib.encodePng(dst));

    // Encode the new bytes to base64 string
    return newBytes;
  } catch (e) {
    debugPrint('Error adding background color to image: $e');
    return null;
  }
}