import 'package:kairos/core/di/injection_container.dart';
import 'package:kairos/core/enums/header_enums.dart';
import 'package:kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:kairos/features/schools/data/datasources/schools_local_datasource.dart';
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart'; // Import EtablissementUtilisateur
import 'package:kairos/features/profile/domain/entities/carte_virtuelle_entity.dart'; // Import CarteVirtuelleEntity
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'dart:convert';
import 'dart:typed_data';

import 'package:kairos/features/profile/presentation/pages/profile/profile.page.dart';
import 'package:kairos/features/schools/data/models/user_profile_model.dart'; // Import UserProfileModel
import 'package:kairos/core/utils/navigation_utils.dart';

class HeroWidget extends StatefulWidget {
  const HeroWidget({
    super.key,
    this.heroTag,
    this.pageSection = HeaderEnum.dashboard,
    this.enfantDuTuteur,
    this.etablissementUser, // Add the new optional parameter
    this.carteVirtuelle, // New optional parameter for CarteVirtuelleEntity
  });

  final HeaderEnum pageSection;
  final String? heroTag;
  final EnfantTuteurEntity? enfantDuTuteur;
  final EtablissementUtilisateur? etablissementUser; // Declare the new parameter
  final CarteVirtuelleEntity? carteVirtuelle; // Declare the new parameter

  @override
  State<HeroWidget> createState() => _HeroWidgetState();
}

class _HeroWidgetState extends State<HeroWidget> {
  // Use late to initialize fullName and data source
  late final String? fullName; // Keep main user's full name for fallback
  late final SchoolsLocalDataSource _schoolsLocalDataSource;

  Uint8List? _profileImageBytes;
  bool _isLoadingImage = true;
  bool _hasImageError = false;
  String? _displayedFullName; // State variable for the displayed full name

  @override
  void initState() {
    super.initState();
    // Initialize data sources
    _schoolsLocalDataSource = sl<SchoolsLocalDataSource>();
    fullName = sl<AuthLocalDataSource>().getFullName(); // Get main user's full name

    // Load user profile data based on whether etablissementUser is provided
    if (widget.etablissementUser != null) {
      _loadSpecificUserProfileData(widget.etablissementUser!);
    } else {
      // If no specific user is provided, hide the profile section immediately
      _isLoadingImage = false;
      _profileImageBytes = null;
      _displayedFullName = fullName;
    }
  }

  @override
  void didUpdateWidget(covariant HeroWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // If the etablissementUser changes, reload the specific user profile data
    if (widget.etablissementUser != oldWidget.etablissementUser) {
      if (widget.etablissementUser != null) {
        _loadSpecificUserProfileData(widget.etablissementUser!);
      } else {
        // If etablissementUser becomes null, reset to default full name
        setState(() {
          _isLoadingImage = false;
          _profileImageBytes = null;
          _displayedFullName = fullName;
        });
      }
    }
  }

  /// Load user profile data (including image and full name) for a specific
  /// user profile from SharedPreferences based on the provided codeUtilisateur.
  Future<void> _loadSpecificUserProfileData(EtablissementUtilisateur etablissementUtilisateur) async {
    if (!mounted) return; // Check if the widget is still mounted

    final codeUtilisateur = etablissementUtilisateur.codeUtilisateur;
    setState(() {
      _isLoadingImage = true;
      _hasImageError = false;
      _profileImageBytes = null;
      _displayedFullName = null;
    });

    try {
      debugPrint("HeroWidget: Loading specific user profile data for codeUtilisateur: $codeUtilisateur");
      final UserProfileModel? userProfile = await _schoolsLocalDataSource.getUserProfile(codeUtilisateur);

      if (userProfile != null) {
        // Update state with loaded data
        if (mounted) {
          setState(() {
            debugPrint("HeroWidget: Updating the full name of the user: ${etablissementUtilisateur.prenom} ${etablissementUtilisateur.nom}");
            _displayedFullName = "${etablissementUtilisateur.codeUtilisateur} - ${etablissementUtilisateur.prenom} ${etablissementUtilisateur.nom}";
            if (userProfile.photo.isNotEmpty) {
              // Decode the base64 string
              debugPrint("HeroWidget: Decoding profile image for codeUtilisateur: ${userProfile.photo}");
              try {
                _profileImageBytes = base64Decode(userProfile.photo);
              } catch (e) {
                debugPrint('Error decoding profile image for $codeUtilisateur: $e');
                _hasImageError = true;
                _profileImageBytes = null; // Use default if decoding fails
              }
            } else {
              _profileImageBytes = null; // Use default if photo is empty
            }
            _isLoadingImage = false;
          });
        }
      } else {
        // Profile not found
        debugPrint("HeroWidget: User profile not found for codeUtilisateur: $codeUtilisateur");
        if (mounted) {
          setState(() {
            _isLoadingImage = false;
            _profileImageBytes = null; // Use default
            _displayedFullName = fullName; // Indicate profile not found
          });
        }
      }
    } catch (e) {
      // Error loading profile
      debugPrint('Error loading specific user profile for $codeUtilisateur: $e');
      if (mounted) {
        setState(() {
          _isLoadingImage = false;
          _hasImageError = true;
          _profileImageBytes = null; // Use default
          _displayedFullName = "Error Loading Profile"; // Indicate error
        }
        );
      }
    }
  }

  /// Build the profile avatar widget
  Widget _buildProfileAvatar() {
    if (_isLoadingImage) {
      return CircleAvatar(
        radius: 33,
        backgroundColor: Colors.grey[300],
        child: const SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      );
    }

    if (_profileImageBytes != null && !_hasImageError) {
      return CircleAvatar(
        radius: 33,
        backgroundImage: MemoryImage(_profileImageBytes!),
        onBackgroundImageError: (exception, stackTrace) {
          debugPrint('Error displaying profile image: $exception');
          if (mounted) {
            setState(() {
              _hasImageError = true;
            });
          }
        },
      );
    }

    // Default fallback image
    return CircleAvatar(
      radius: 33,
      backgroundImage: const AssetImage("assets/images/default_profile_image.jpg"),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: widget.heroTag ?? "hero_dashboard",
      transitionOnUserGestures: true,
      child: Stack(
        children: [
          Image.asset(
            widget.pageSection == HeaderEnum.dashboard ? "assets/images/header_dashboard.png" :
            widget.pageSection == HeaderEnum.notes ? "assets/images/header_notes.png" :
            widget.pageSection == HeaderEnum.absences ? "assets/images/header_absences.png" :
            widget.pageSection == HeaderEnum.cahierDeTexte ? "assets/images/header_cahier_texte.png" :
            widget.pageSection == HeaderEnum.planning ? "assets/images/header_planning.png" :
            widget.pageSection == HeaderEnum.dossiers ? "assets/images/header_dossiers.png" :
            widget.pageSection == HeaderEnum.notifications ? "assets/images/header_dossiers.png" :
            widget.pageSection == HeaderEnum.finances ? "assets/images/header_finances.png" :
            widget.pageSection == HeaderEnum.controleFinancier ? "assets/images/header_dashboard.png" :
            "assets/images/header_dashboard.png",
            height: 189,
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.cover,
          ),
          Positioned(
            top: 60,
            left: 20,
            width: MediaQuery.of(context).size.width - 40,
            child: Column(
                    children: [
                      // Display profile section only if etablissementUser is provided
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start, // Align text to start
                              mainAxisAlignment: MainAxisAlignment.start,
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                SvgPicture.asset(
                                  widget.pageSection == HeaderEnum.dashboard
                                      ? "assets/images/logo_kairos.svg"
                                      : "assets/images/logo_kairos_blanc.svg",
                                ),
                                SizedBox(height: 10),
                                // Display the loaded full name
                                Material(
                                  color: Colors.transparent,
                                  child: Text(
                                    _displayedFullName ?? "",
                                    textAlign: TextAlign.left,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            // Display the loaded profile avatar
                           if(widget.etablissementUser != null)
                           GestureDetector(
                              onTap: () {
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) => ProfilePage(etablissementUser: widget.etablissementUser!,),
                                  ),
                                );
                              },
                              child:_buildProfileAvatar(),
                           ),
                           ],
                        ),
                       // The existing enfantDuTuteur section remains
                      if(widget.enfantDuTuteur != null)
                        Divider(color: Colors.white, thickness: 1, height: 10, indent: 10, endIndent: 10,),
                      if(widget.enfantDuTuteur != null)
                        Material( // Wrap ListTile with Material
                          color: Colors.transparent, // Make Material transparent if needed
                          child: ListTile(
                            leading: CircleAvatar(
                              radius: 20,
                              backgroundColor: Colors.grey[300],
                              backgroundImage: widget.enfantDuTuteur!.photo.isNotEmpty && widget.enfantDuTuteur!.photo != "null"
                                  ? MemoryImage(base64Decode(widget.enfantDuTuteur!.photo))
                                  : null,
                              child: widget.enfantDuTuteur!.photo.isEmpty
                                  ? const Icon(Icons.person, color: Colors.grey)
                                  : null,
                            ),
                            title: Text(
                              '${widget.enfantDuTuteur!.codeEtudiant} - ${widget.enfantDuTuteur!.prenom} ${widget.enfantDuTuteur!.nom}',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                            trailing: IconButton(
                              icon: Image.asset('assets/icons/switch_account.png', width: 20, height: 20,),
                              onPressed: () {
                                debugPrint('Switching account --------> ');
                                NavigationUtils.popUntil(
                                  context,
                                  ModalRoute.withName('/dossier_selection'),
                                );
                              },
                            ),
                          ),
                        ),
                    ],
                  ),
            ),

            // Student information overlay for controle financier page
            if (widget.pageSection == HeaderEnum.controleFinancier)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 194,
                  decoration: const BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.only(
                      bottomRight: Radius.circular(71),
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Student avatar
                      Positioned(
                        top: 57,
                        right: 45,
                        child: Container(
                          width: 69,
                          height: 69,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.grey,
                            image: widget.carteVirtuelle?.photo != null && widget.carteVirtuelle!.photo!.isNotEmpty
                                ? DecorationImage(
                                    image: MemoryImage(base64Decode(widget.carteVirtuelle!.photo!)),
                                    fit: BoxFit.cover,
                                  )
                                : null,
                          ),
                          child: widget.carteVirtuelle?.photo == null || widget.carteVirtuelle!.photo!.isEmpty
                              ? const Icon(
                                  Icons.person,
                                  color: Colors.white,
                                  size: 40,
                                )
                              : null,
                        ),
                      ),

                      // Student information
                      Positioned(
                        left: 17,
                        top: 16,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Matricule
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Matricule',
                                  style: TextStyle(
                                   
                                    fontWeight: FontWeight.w400,
                                    fontSize: 12,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 5),
                                Text(
                                  widget.carteVirtuelle?.codeUtilisateur ?? '',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w700,
                                    fontSize: 14,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 10),

                            // Etudiant
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Etudiant',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 12,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 5),
                                Text(
                                  '${widget.carteVirtuelle?.prenom ?? ''} ${widget.carteVirtuelle?.nom ?? ''}',
                                  style: const TextStyle(
                                   
                                    fontWeight: FontWeight.w700,
                                    fontSize: 14,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 10),

                            // Niveau
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Niveau',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 12,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 5),
                                Text(
                                  widget.carteVirtuelle?.niveauEnCours ?? 'N/A',
                                  style: const TextStyle(
                                   
                                    fontWeight: FontWeight.w700,
                                    fontSize: 14,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 10),

                            // Classe
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Classe',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 12,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 5),
                                Text(
                                  widget.carteVirtuelle?.classeEnCours ?? 'N/A',
                                  style: const TextStyle(
                                   
                                    fontWeight: FontWeight.w700,
                                    fontSize: 14,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
        ],
          ),
    );
  }
}