import 'package:flutter/material.dart';
import 'dart:convert'; // Required for base64Decode
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../bloc/schools_cubit.dart';
import '../../bloc/schools_state.dart';

/// A bottom modal widget for selecting a school from a list.
class SchoolSelectionModal extends StatelessWidget {
  const SchoolSelectionModal({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Theme.of(context).colorScheme.secondary,
                ),
                child: IconButton(
                  icon: Icon(Icons.close, color: Colors.white),
                  onPressed: () {
                    Navigator.pop(context); // Close the modal
                  },
                ),
              ),
            ],
          ),
        ),
        Text(
                'LISTE DES ÉTABLISSEMENTS',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
       Divider(color: Theme.of(context).primaryColor, thickness: 4, height: 20, indent: 100, endIndent: 100,),
        // Use BlocBuilder to react to SchoolsCubit states
        Expanded(
          child: BlocBuilder<SchoolsCubit, SchoolsState>(
            builder: (context, state) {
              debugPrint("SchoolSelectionModal state: $state");
              // Display a loading indicator while schools are being loaded.
              if (state is SchoolsLoading) {
                return const Center(child: CircularProgressIndicator());
              }
              // Display an error message if there was an error loading schools.
              else if (state is SchoolsError) {
                return Center(child: Text('Error: ${state.message}'));
              }
              // Display an empty message if no schools were found.
              else if (state is SchoolsEmpty) {
                return const Center(child: Text('No schools found.'));
              }
              // Display the list of schools when the state is GeneralSchoolsLoaded.
              else if (state is GeneralSchoolsLoaded) {
                final schools = state.data;
                return ListView.builder(
                  itemCount: schools.length,
                  itemBuilder: (context, index) {
                    final school = schools[index];
                    // Display each school as a list tile.
                    return Card(
                      margin: EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
                      child: InkWell(
                        onTap: () {
                          // Return the selected school when tapped.
                          Navigator.pop(context, school);
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Row(
                            children: [
                              // Check if the school logo is available and not empty.
                              // The logoEtablissement is a base64 string, so we use Image.memory
                              // along with base64Decode to display it.
                              school.logoEtablissement.isNotEmpty
                                  ? Image.memory(
                                      base64Decode(school.logoEtablissement),
                                      width: 40,
                                      height: 40,
                                      // Provide a placeholder icon if the image fails to load or decode.
                                      errorBuilder: (context, error, stackTrace) => const Icon(Icons.school),
                                    )
                                  : const Icon(Icons.school), // Default icon if no logo is provided
                              SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  school.libelleEtab, // Display school name
                                  style: TextStyle(fontSize: 16),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              }
              // Default case for initial or unhandled states.
              return const Center(child: Text('Select a school'));
            },
          ),
        ),
      ],
    );
  }
}