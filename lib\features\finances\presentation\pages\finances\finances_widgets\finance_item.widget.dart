import 'package:kairos/core/theme/color_schemes.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:kairos/core/utils/date_utils.dart' as du;

class FinanceItem extends StatelessWidget {
  final String intituleFrais;
  final int montantFrais;
  final String status;
  final String? dateEchance;
  final bool isPaid;
  final bool isObligatory;
  final String? dateQuittance;
  final int? montantEncaisseAjour;
  final String? numeroQuittance;

  const FinanceItem({
    super.key,
    required this.intituleFrais,
    required this.montantFrais,
    required this.dateEchance,
    required this.status,
    this.isPaid = false,
    this.isObligatory = false,
    this.dateQuittance,
    this.montantEncaisseAjour,
    this.numeroQuittance,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 7.0, vertical: 4.0),
      height: 60,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Theme.of(context).colorScheme.secondary, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.25),
            offset: const Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
        child: Padding(
          padding: const EdgeInsets.all(5.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      intituleFrais,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (montantEncaisseAjour != null)
                      Text(
                        '${NumberFormat.decimalPattern('fr_FR').format(montantFrais)} XOF${isPaid == false? ' ${dateEchance != null? 'Á PAYER AVANT LE' : ''} ${dateEchance ?? ''}' : ''}',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    if (!isPaid && montantFrais > 0 && numeroQuittance == null)
                      Text(
                        status,
                        style: TextStyle(
                          fontSize: 8,
                          color: Colors.grey,
                        ),
                      ),
                    if (numeroQuittance != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 0.0),
                        child: Text(
                          "Quittance n° ${numeroQuittance!}",
                          style: TextStyle(
                            fontSize: 8,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  if (isObligatory)
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColorSchemes.errorRed,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'Obligatoire',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  if (dateQuittance != null)
                    Text(
                      'Soldé le ${du.formatDateFr(dateQuittance!)}',
                      style: TextStyle(
                        fontSize: 8,
                        color: Colors.green,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      );
    
  }
}
